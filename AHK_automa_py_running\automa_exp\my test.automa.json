{"extVersion": "1.29.10", "name": "my test", "icon": "riGlobalLine", "table": [{"id": "8duIp", "name": "ttt", "type": "any"}], "version": "1.29.10", "drawflow": {"edges": [{"data": {}, "id": "vueflow__edge-gwp0qrfgwp0qrf-output-1-omeitkfomeitkf-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "gwp0qrf", "sourceHandle": "gwp0qrf-output-1", "sourceX": 255.23076923076928, "sourceY": 386.00004225510816, "target": "ys5s3ys", "targetHandle": "ys5s3ys-input-1", "targetX": 317.59543789783277, "targetY": 449.8401111992742, "type": "custom", "updatable": true}, {"class": "source-omeitkf-output-1 target-bpck030-input-1", "data": {}, "id": "vueflow__edge-omeitkfomeitkf-output-1-bpck030bpck030-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "omeitkf", "sourceHandle": "omeitkf-output-1", "sourceX": 923.3695153991652, "sourceY": 458.56849248892945, "target": "bpck030", "targetHandle": "bpck030-input-1", "targetX": 835.2307128906249, "targetY": 654.824481670673, "type": "custom", "updatable": true}, {"class": "source-l8bldax-output-1 target-omeitkf-input-1", "data": {}, "id": "vueflow__edge-l8bldaxl8bldax-output-1-omeitkfomeitkf-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "l8bldax", "sourceHandle": "l8bldax-output-1", "sourceX": 829.8460599459135, "sourceY": 294.46153376652643, "target": "omeitkf", "targetHandle": "omeitkf-input-1", "targetX": 683.3695529592612, "targetY": 458.56849248892945, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-ys5s3ysys5s3ys-output-1-l8bldaxl8bldax-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "ys5s3ys", "sourceHandle": "ys5s3ys-output-1", "sourceX": 557.5954003377367, "sourceY": 449.8401111992742, "target": "l8bldax", "targetHandle": "l8bldax-input-1", "targetX": 589.8460975060095, "targetY": 294.46153376652643, "type": "custom", "updatable": true}, {"class": "source-bpck030-output-1 target-t7k8nvi-input-1", "data": {}, "id": "vueflow__edge-bpck030bpck030-output-1-t7k8nvit7k8nvi-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "bpck030", "sourceHandle": "bpck030-output-1", "sourceX": 1075.2306753305288, "sourceY": 654.824481670673, "target": "t7k8nvi", "targetHandle": "t7k8nvi-input-1", "targetX": 1056.3845590444707, "targetY": 505.230741060697, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-t7k8nvit7k8nvi-output-1-anawwgxanawwgx-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "t7k8nvi", "sourceHandle": "t7k8nvi-output-1", "sourceX": 1296.384615384615, "sourceY": 505.230741060697, "target": "anawwgx", "targetHandle": "anawwgx-input-1", "targetX": 1283.692157451923, "targetY": 335.2307645357572, "type": "custom", "updatable": true}], "nodes": [{"data": {"addExtraRow": false, "assignVariable": false, "dataColumn": "8duIp", "description": "", "disableBlock": false, "extraRowDataColumn": "8duIp", "extraRowValue": "", "findBy": "cssSelector", "includeTags": false, "markEl": true, "multiple": false, "prefixText": "", "regex": "", "regexExp": [], "saveData": true, "selector": ".List\\(n\\):nth-child({{loopData.nDZcaN}}) > .Bgc\\(\\#fff\\)", "suffixText": "", "useTextContent": false, "variableName": "", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "omeitkf", "initialized": false, "label": "get-text", "position": {"x": 707.3696092994055, "y": 422.56847370888136}, "type": "BlockBasic"}, {"data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "id": "gwp0qrf", "initialized": false, "label": "trigger", "position": {"x": 39.23076923076928, "y": 350}, "type": "BlockBasic"}, {"data": {"active": true, "customUserAgent": false, "description": "", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "https://tw.stock.yahoo.com/future/options.html?opmr=optionfull&opcm=WTXO&opym=202507", "userAgent": "", "waitTabLoaded": true}, "id": "ys5s3ys", "initialized": false, "label": "new-tab", "position": {"x": 341.59540033773663, "y": 413.8400924192261}, "type": "BlockBasic"}, {"data": {"description": "", "disableBlock": false, "elementSelector": "", "fromNumber": 1, "loopData": "[]", "loopId": "nDZcaN", "loopThrough": "numbers", "maxLoop": 0, "referenceKey": "", "resumeLastWorkflow": false, "reverseLoop": false, "startIndex": 0, "toNumber": 100, "variableName": "", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "l8bldax", "initialized": false, "label": "loop-data", "position": {"x": 613.8461538461538, "y": 258.46153846153845}, "type": "BlockBasic"}, {"data": {"clearLoop": false, "disableBlock": false, "loopId": "nDZcaN"}, "id": "bpck030", "initialized": false, "label": "loop-breakpoint", "position": {"x": 859.2307692307692, "y": 579.2307692307692}, "type": "BlockLoopBreakpoint"}, {"data": {"addBOMHeader": true, "csvDelimiter": ",", "dataToExport": "data-columns", "description": "", "disableBlock": false, "name": "my test", "onConflict": "uniquify", "refKey": "", "type": "json", "variableName": ""}, "id": "anawwgx", "initialized": false, "label": "export-data", "position": {"x": 1307.6923076923076, "y": 299.2307692307692}, "type": "BlockBasic"}, {"data": {"activeTab": true, "allWindows": false, "closeType": "tab", "description": "", "disableBlock": false, "url": ""}, "id": "t7k8nvi", "initialized": false, "label": "close-tab", "position": {"x": 1080.384615384615, "y": 469.23076923076917}, "type": "BlockBasic"}], "position": [-480, -98.99999999999994], "viewport": {"x": -480, "y": -98.99999999999994, "zoom": 1.3}, "zoom": 1.3}, "settings": {"blockDelay": 0, "debugMode": false, "defaultColumnName": "column", "execContext": "popup", "executedBlockOnWeb": false, "inputAutocomplete": true, "insertDefaultColumn": false, "notification": true, "onError": "stop-workflow", "publicId": "", "restartTimes": 3, "reuseLastState": false, "saveLog": true, "tabLoadTimeout": 30000}, "globalData": "{\n\t\"key\": \"value\"\n}", "description": "", "includedWorkflows": {}}