#Requires AutoHotkey v2.0
#SingleInstance Force

CoordMode("Mouse", "Screen") ; 確保所有滑鼠操作都使用絕對螢幕座標

; 指定 Chrome 視窗
chrome := "ahk_exe msedge.exe"

; 檢查 Chrome 是否存在
if WinExist(chrome) {
    WinActivate(chrome)      ; 可選：將 Chrome 視窗切換到前景
    
    Sleep 300                ; 等待視窗切換完成
;    WinMaximize(chrome)      ; 最大化 Chrome 視窗
    SendMode "Input"
    SendInput "!d" ;-focus to address bar
    Sleep 100                ; 等待網址列獲得焦點
    SendText "chrome-extension://infppggnoaenmfagbfknfkancpbljcca/execute.html#/nfP6peNy0NW1UHeLybywx"
    Sleep 100                ; 等待網址列獲得焦點
    SendInput "{enter}"
}

;Sleep 500  ; delay 之後
;Click 1740, 70

Sleep 9500  ; delay .5sec
Send("{Enter}")    ; 按 enter 鍵確認刪除

Sleep 500  ; delay .5sec
Run "python json_to_clipboard.py" ; python 執行目前的路徑下的檔案 json_to_clipboard.py
