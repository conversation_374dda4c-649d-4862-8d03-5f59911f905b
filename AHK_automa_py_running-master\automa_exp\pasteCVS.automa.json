{"extVersion": "1.29.10", "name": "pasteCVS", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"edges": [{"data": {}, "id": "vueflow__edge-nFm7iGcjJy4NI_9ryYLKinFm7iGcjJy4NI_9ryYLKi-output-1-3gg5hgp3gg5hgp-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "nFm7iGcjJy4NI_9ryYLKi", "sourceHandle": "nFm7iGcjJy4NI_9ryYLKi-output-1", "sourceX": 315.9999511330193, "sourceY": 544.4999497928944, "target": "3gg5hgp", "targetHandle": "3gg5hgp-input-1", "targetX": 352.1538272374676, "targetY": 416.0000117439348, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-bv120z0bv120z0-output-1-efq8ddlefq8ddl-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "bv120z0", "sourceHandle": "bv120z0-output-1", "sourceX": 842.4823616660176, "sourceY": 700.4778294037582, "target": "efq8ddl", "targetHandle": "efq8ddl-input-1", "targetX": 773.4131667404512, "targetY": 563.2179640952227, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-3gg5hgp3gg5hgp-output-1-bv120z0bv120z0-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "3gg5hgp", "sourceHandle": "3gg5hgp-output-1", "sourceX": 592.153735335825, "sourceY": 416.0000117439348, "target": "bv120z0", "targetHandle": "bv120z0-input-1", "targetX": 602.4823916166198, "targetY": 700.4778294037582, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-efq8ddlefq8ddl-output-1-pzulpqlpzulpql-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "efq8ddl", "sourceHandle": "efq8ddl-output-1", "sourceX": 1013.413136789849, "sourceY": 563.2179640952227, "target": "pzulpql", "targetHandle": "pzulpql-input-1", "targetX": 849.9202204493618, "targetY": 397.6057745026256, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-pzulpqlpzulpql-output-1-ioxsjowioxsjow-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "pzulpql", "sourceHandle": "pzulpql-output-1", "sourceX": 1089.920128547719, "sourceY": 397.6057745026256, "target": "ioxsjow", "targetHandle": "ioxsjow-input-1", "targetX": 1131.4876374699193, "targetY": 399.72013580119113, "type": "custom", "updatable": true}], "nodes": [{"data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "id": "nFm7iGcjJy4NI_9ryYLKi", "initialized": false, "label": "trigger", "position": {"x": 100, "y": 508.5}, "type": "BlockBasic"}, {"data": {"activeTab": true, "createIfNoMatch": false, "description": "", "disableBlock": false, "findTabBy": "match-patterns", "matchPattern": "*://docs.google.com/*", "tabIndex": 0, "tabTitle": "", "url": ""}, "id": "3gg5hgp", "initialized": false, "label": "switch-tab", "position": {"x": 376.15384615384613, "y": 380}, "type": "BlockBasic"}, {"data": {"action": "press-key", "description": "", "disableBlock": false, "keys": "Enter", "keysToPress": "", "pressTime": "100", "selector": "input#t-name-box"}, "id": "efq8ddl", "initialized": false, "label": "press-key", "position": {"x": 797.4131856568297, "y": 527.2179523512879}, "type": "BlockBasic"}, {"data": {"assignVariable": false, "clearValue": true, "dataColumn": "", "delay": 0, "description": "", "disableBlock": false, "events": [], "findBy": "cssSelector", "getValue": false, "markEl": false, "multiple": false, "optionPosition": "1", "saveData": false, "selectOptionBy": "value", "selected": true, "selector": "input#t-name-box", "type": "text-field", "value": "XslT_Q!A306", "variableName": "", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "bv120z0", "initialized": false, "label": "forms", "position": {"x": 626.4824105329983, "y": 664.4778176598234}, "type": "BlockBasic"}, {"data": {"description": "", "disableBlock": false, "eventName": "keydown", "eventParams": {"altKey": false, "bubbles": true, "cancelable": true, "code": "KeyV", "ctrlKey": true, "key": "V", "keyCode": 86, "metaKey": false, "repeat": false, "shiftKey": false}, "eventType": "keyboard-event", "findBy": "cssSelector", "markEl": false, "multiple": false, "selector": "html", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "ioxsjow", "initialized": false, "label": "trigger-event", "position": {"x": 1155.4875944352573, "y": 363.7201550327765}, "type": "BlockBasic"}, {"data": {"disableBlock": false, "time": "1000"}, "id": "pzulpql", "initialized": false, "label": "delay", "position": {"x": 873.9202393657403, "y": 339.01203246011283}, "type": "BlockDelay"}], "position": [-491, -95], "viewport": {"x": -491, "y": -95, "zoom": 0.9852157682317587}, "zoom": 0.9852157682317587}, "settings": {"blockDelay": 0, "debugMode": false, "defaultColumnName": "column", "execContext": "popup", "executedBlockOnWeb": false, "inputAutocomplete": true, "insertDefaultColumn": false, "notification": true, "onError": "stop-workflow", "publicId": "", "restartTimes": 3, "reuseLastState": false, "saveLog": true}, "globalData": "{\n\t\"key\": \"value\"\n}", "description": "", "includedWorkflows": {}}