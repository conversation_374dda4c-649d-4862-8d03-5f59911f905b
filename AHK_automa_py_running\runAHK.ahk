#Requires AutoHotkey v2.0
#SingleInstance Force

CoordMode("Mouse", "Screen") ; 確保所有滑鼠操作都使用絕對螢幕座標

; 指定 Chrome 視窗
chrome := "ahk_exe chrome.exe"

; 檢查 Chrome 是否存在
if WinExist(chrome) {
    WinActivate(chrome)      ; 可選：將 Chrome 視窗切換到前景
    WinMaximize(chrome)      ; 最大化 Chrome 視窗
}

Sleep 500  ; delay 之後
Click 1740, 55

Sleep 500  ; delay 之後
Click 1670, 485 ; 點擊 run automa

Sleep 8000  ; delay 9sec
;WinActivate(chrome) ; 確保 Chrome 視窗獲得焦點
;Click 469, 176, 2 ; 雙擊檔案名my test.json

;Sleep 500  ; delay .5sec
;Send "y"    ; 按Y鍵.

;Sleep 500  ; delay .5sec
Run "python json_to_clipboard.py" ; python 執行目前的路徑下的檔案 json_to_clipboard.py
