import json
import csv
import re
import os
import pyperclip # Import pyperclip

def convert_json_to_csv(json_file_path):
    """
    Converts a JSON file with a specific structure to a tab-separated string,
    removes commas from numerical values, and copies it to the clipboard.
    """
    processed_data = []
    max_columns = 0

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
    except FileNotFoundError:
        print(f"Error: JSON file not found at {json_file_path}")
        return False # Indicate failure
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSO<PERSON> from {json_file_path}")
        return False # Indicate failure

    for item in json_data:
        if "ttt" in item and isinstance(item["ttt"], str):
            values = item["ttt"].split('\n')
            processed_row = []
            for value in values:
                cleaned_value = re.sub(r'(?<=\d),(?=\d)', '', value)
                processed_row.append(cleaned_value)
            processed_data.append(processed_row)
            max_columns = max(max_columns, len(processed_row))
        else:
            print(f"Warning: Skipping item due to missing or invalid 'ttt' key: {item}")

    if not processed_data:
        print("No data to process.")
        return False # Indicate failure

    # No header row as per user's last request
    output_lines = []

    for row in processed_data:
        padded_row = row + [''] * (max_columns - len(row))
        output_lines.append('\t'.join(padded_row))

    clipboard_content = '\n'.join(output_lines)
    try:
        pyperclip.copy(clipboard_content)
        print("Successfully copied data to clipboard. You can now paste it into Google Sheets.")
        return True # Indicate success
    except pyperclip.PyperclipException as e:
        print(f"Error copying to clipboard: {e}")
        print("Please ensure you have a copy/paste mechanism available (e.g., xclip on Linux, or run in a GUI environment).")
        print("Alternatively, you can manually copy the following data:")
        print(clipboard_content)
        return False # Indicate failure


if __name__ == "__main__":
    json_input_path = os.path.join('..', '..', 'pic', 'test', 'my test.json')
    convert_json_to_csv(json_input_path)