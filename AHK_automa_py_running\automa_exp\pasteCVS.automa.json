{"extVersion": "1.29.10", "name": "pasteCVS", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"edges": [{"data": {}, "id": "vueflow__edge-nFm7iGcjJy4NI_9ryYLKinFm7iGcjJy4NI_9ryYLKi-output-1-3gg5hgp3gg5hgp-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "nFm7iGcjJy4NI_9ryYLKi", "sourceHandle": "nFm7iGcjJy4NI_9ryYLKi-output-1", "sourceX": 315.9999511330193, "sourceY": 544.4999497928944, "target": "3gg5hgp", "targetHandle": "3gg5hgp-input-1", "targetX": 352.1538272374676, "targetY": 416.0000117439348, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-bv120z0bv120z0-output-1-efq8ddlefq8ddl-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "bv120z0", "sourceHandle": "bv120z0-output-1", "sourceX": 842.4823616660176, "sourceY": 700.4778294037582, "target": "efq8ddl", "targetHandle": "efq8ddl-input-1", "targetX": 891.1539345758422, "targetY": 630.2083657190773, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-efq8ddlefq8ddl-output-1-ioxsjowioxsjow-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "efq8ddl", "sourceHandle": "efq8ddl-output-1", "sourceX": 1131.1537807231591, "sourceY": 630.2083657190773, "target": "ioxsjow", "targetHandle": "ioxsjow-input-1", "targetX": 1109.15737969322, "targetY": 512.3858422350124, "type": "custom", "updatable": true}, {"class": "source-ioxsjow-output-1 target-fbpzln2-input-1", "data": {}, "id": "vueflow__edge-ioxsjowioxsjow-output-1-fbpzln2fbpzln2-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "ioxsjow", "sourceHandle": "ioxsjow-output-1", "sourceX": 1349.157349742618, "sourceY": 512.3858422350124, "target": "fbpzln2", "targetHandle": "fbpzln2-input-1", "targetX": 1336.7592090473597, "targetY": 701.971337180816, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-3gg5hgp3gg5hgp-output-1-bv120z0bv120z0-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "3gg5hgp", "sourceHandle": "3gg5hgp-output-1", "sourceX": 592.153735335825, "sourceY": 416.0000117439348, "target": "bv120z0", "targetHandle": "bv120z0-input-1", "targetX": 602.4823916166198, "targetY": 700.4778294037582, "type": "custom", "updatable": true}], "nodes": [{"data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "id": "nFm7iGcjJy4NI_9ryYLKi", "initialized": false, "label": "trigger", "position": {"x": 100, "y": 508.5}, "type": "BlockBasic"}, {"data": {"activeTab": true, "createIfNoMatch": false, "description": "", "disableBlock": false, "findTabBy": "match-patterns", "matchPattern": "*://docs.google.com/*", "tabIndex": 0, "tabTitle": "", "url": ""}, "id": "3gg5hgp", "initialized": false, "label": "switch-tab", "position": {"x": 376.15384615384613, "y": 380}, "type": "BlockBasic"}, {"data": {"action": "press-key", "description": "", "disableBlock": false, "keys": "Enter", "keysToPress": "", "pressTime": "100", "selector": "input#t-name-box"}, "id": "efq8ddl", "initialized": false, "label": "press-key", "position": {"x": 915.1538915411802, "y": 594.2083539751425}, "type": "BlockBasic"}, {"data": {"assignVariable": false, "clearValue": true, "dataColumn": "", "delay": 0, "description": "", "disableBlock": false, "events": [], "findBy": "cssSelector", "getValue": false, "markEl": false, "multiple": false, "optionPosition": "1", "saveData": false, "selectOptionBy": "value", "selected": true, "selector": "input#t-name-box", "type": "text-field", "value": "XslT_Q!A306", "variableName": "", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "bv120z0", "initialized": false, "label": "forms", "position": {"x": 626.4824105329983, "y": 664.4778176598234}, "type": "BlockBasic"}, {"data": {"description": "", "disableBlock": false, "eventName": "keydown", "eventParams": {"altKey": false, "bubbles": true, "cancelable": true, "code": "KeyV", "ctrlKey": true, "key": "v", "keyCode": 86, "metaKey": false, "repeat": false, "shiftKey": false}, "eventType": "keyboard-event", "findBy": "cssSelector", "markEl": false, "multiple": false, "selector": "html", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "ioxsjow", "initialized": false, "label": "trigger-event", "position": {"x": 1133.157460560639, "y": 476.38583049107757}, "type": "BlockBasic"}, {"data": {"description": "", "disableBlock": false, "eventName": "keyup", "eventParams": {"altKey": true, "bubbles": true, "cancelable": true, "code": "Digit1", "ctrlKey": true, "key": "1", "keyCode": 49, "metaKey": false, "repeat": false, "shiftKey": true}, "eventType": "keyboard-event", "findBy": "cssSelector", "markEl": false, "multiple": false, "selector": "html", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "fbpzln2", "initialized": false, "label": "trigger-event", "position": {"x": 1360.7591660126977, "y": 665.9713254368812}, "type": "BlockBasic"}], "position": [0, 0], "viewport": {"x": 0, "y": 0, "zoom": 0.9852157682317587}, "zoom": 0.9852157682317587}, "settings": {"blockDelay": 0, "debugMode": false, "defaultColumnName": "column", "execContext": "popup", "executedBlockOnWeb": false, "inputAutocomplete": true, "insertDefaultColumn": false, "notification": true, "onError": "stop-workflow", "publicId": "", "restartTimes": 3, "reuseLastState": false, "saveLog": true}, "globalData": "{\n\t\"key\": \"value\"\n}", "description": "", "includedWorkflows": {}}