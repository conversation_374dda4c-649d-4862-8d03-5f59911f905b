# 自動化更新 Google Sheet M_Options 資料

這個程式旨在自動化更新當月選擇權的 Google Sheet M_Options 資料。 
## Automa 的Json如下:
1. 會自動到yahoo期權抓資料. my test
2. export to my test.json
3. python json_to_clipboard.py 轉成google sheet table 到clipboard, 方便ctrl+v 貼上.
4. 到google sheet XstT_Q!A306 貼上後, 再Ref_All.


## 前置步驟

在執行此自動化程式之前，您需要確保已匯入 Automa 的 JSON 檔案。

### 產生 `my test.json` 檔案

首次執行時，您需要手動執行 `my test.automa.json` 這個 Automa 檔案。這將會產生一個名為 `my test.json` 的檔案，並將其儲存到以下固定路徑：

`..\..\pic\test\my test.json`

請確保此檔案存在於指定路徑，因為本自動化程式將依賴此檔案進行資料處理。

## CMD /c run.bat