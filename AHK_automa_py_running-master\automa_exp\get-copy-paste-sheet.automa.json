{"extVersion": "1.29.10", "name": "get-copy-paste-sheet", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"edges": [{"data": {}, "id": "vueflow__edge-PDEOVGFtFL1Wwo7sbR-I_PDEOVGFtFL1Wwo7sbR-I_-output-1-cdkrnascdkrnas-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "PDEOVGFtFL1Wwo7sbR-I_", "sourceHandle": "PDEOVGFtFL1Wwo7sbR-I_-output-1", "sourceX": 316, "sourceY": 544.4999718299279, "target": "cdkrnas", "targetHandle": "cdkrnas-input-1", "targetX": 367.53840519831715, "targetY": 503.6923264723557, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-cdkrnascdkrnas-output-1-v1sfvutv1sfvut-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "cdkrnas", "sourceHandle": "cdkrnas-output-1", "sourceX": 607.5384145883412, "sourceY": 503.6923264723557, "target": "v1sfvut", "targetHandle": "v1sfvut-input-1", "targetX": 377.5384521484374, "targetY": 674.7476243239183, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-v1sfvutv1sfvut-output-1-4ackfap4ackfap-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "v1sfvut", "sourceHandle": "v1sfvut-output-1", "sourceX": 617.5384615384614, "sourceY": 674.7476243239183, "target": "4ackfap", "targetHandle": "4ackfap-input-1", "targetX": 727.5384051983173, "targetY": 565.230741060697, "type": "custom", "updatable": true}], "nodes": [{"data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "id": "PDEOVGFtFL1Wwo7sbR-I_", "initialized": false, "label": "trigger", "position": {"x": 100, "y": 508.5}, "type": "BlockBasic"}, {"data": {"description": "", "disableBlock": false, "executeId": "", "globalData": "", "insertAllGlobalData": false, "insertAllVars": false, "workflowId": "DawqFCcv6NFFf0dgspWJY"}, "id": "cdkrnas", "initialized": false, "label": "execute-workflow", "position": {"x": 391.5384615384614, "y": 467.6923076923076}, "type": "BlockBasic"}, {"data": {"disableBlock": false, "time": "8000"}, "id": "v1sfvut", "initialized": false, "label": "delay", "position": {"x": 401.53846153846143, "y": 616.1538461538462}, "type": "BlockDelay"}, {"data": {"description": "", "disableBlock": false, "executeId": "", "globalData": "", "insertAllGlobalData": false, "insertAllVars": false, "workflowId": "A5l7jFdVLP1IeyGRCgRdy"}, "id": "4ackfap", "initialized": false, "label": "execute-workflow", "position": {"x": 751.5384615384615, "y": 529.2307692307692}, "type": "BlockBasic"}], "position": [-34, -109], "viewport": {"x": -34, "y": -109, "zoom": 1.3}, "zoom": 1.3}, "settings": {"blockDelay": 0, "debugMode": false, "defaultColumnName": "column", "execContext": "popup", "executedBlockOnWeb": false, "inputAutocomplete": true, "insertDefaultColumn": false, "notification": true, "onError": "stop-workflow", "publicId": "", "restartTimes": 3, "reuseLastState": false, "saveLog": true}, "globalData": "{\n\t\"key\": \"value\"\n}", "description": "", "includedWorkflows": {"DawqFCcv6NFFf0dgspWJY": {"extVersion": "1.29.10", "name": "my test", "icon": "riGlobalLine", "table": [{"id": "8duIp", "name": "ttt", "type": "any"}], "version": "1.29.10", "drawflow": {"edges": [{"data": {}, "id": "vueflow__edge-gwp0qrfgwp0qrf-output-1-omeitkfomeitkf-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "gwp0qrf", "sourceHandle": "gwp0qrf-output-1", "sourceX": 255.23076923076928, "sourceY": 386.00004225510816, "target": "ys5s3ys", "targetHandle": "ys5s3ys-input-1", "targetX": 317.5953909477126, "targetY": 449.8401111992742, "type": "custom", "updatable": true}, {"class": "source-omeitkf-output-1 target-bpck030-input-1", "data": {}, "id": "vueflow__edge-omeitkfomeitkf-output-1-bpck030bpck030-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "omeitkf", "sourceHandle": "omeitkf-output-1", "sourceX": 923.3695623492853, "sourceY": 458.56849248892945, "target": "bpck030", "targetHandle": "bpck030-input-1", "targetX": 835.2307128906249, "targetY": 654.824481670673, "type": "custom", "updatable": true}, {"class": "source-l8bldax-output-1 target-omeitkf-input-1", "data": {}, "id": "vueflow__edge-l8bldaxl8bldax-output-1-omeitkfomeitkf-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "l8bldax", "sourceHandle": "l8bldax-output-1", "sourceX": 829.8461068960336, "sourceY": 294.46153376652643, "target": "omeitkf", "targetHandle": "omeitkf-input-1", "targetX": 683.3695999093815, "targetY": 458.56849248892945, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-ys5s3ysys5s3ys-output-1-l8bldaxl8bldax-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "ys5s3ys", "sourceHandle": "ys5s3ys-output-1", "sourceX": 557.5953768626765, "sourceY": 449.8401111992742, "target": "l8bldax", "targetHandle": "l8bldax-input-1", "targetX": 589.8461444561298, "targetY": 294.46153376652643, "type": "custom", "updatable": true}, {"class": "source-bpck030-output-1 target-t7k8nvi-input-1", "data": {}, "id": "vueflow__edge-bpck030bpck030-output-1-t7k8nvit7k8nvi-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "bpck030", "sourceHandle": "bpck030-output-1", "sourceX": 1075.2306753305288, "sourceY": 654.824481670673, "target": "t7k8nvi", "targetHandle": "t7k8nvi-input-1", "targetX": 1056.3845590444707, "targetY": 505.230741060697, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-t7k8nvit7k8nvi-output-1-anawwgxanawwgx-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "t7k8nvi", "sourceHandle": "t7k8nvi-output-1", "sourceX": 1296.384615384615, "sourceY": 505.230741060697, "target": "anawwgx", "targetHandle": "anawwgx-input-1", "targetX": 1283.6923452524038, "targetY": 335.2307645357572, "type": "custom", "updatable": true}], "nodes": [{"data": {"addExtraRow": false, "assignVariable": false, "dataColumn": "8duIp", "description": "", "disableBlock": false, "extraRowDataColumn": "8duIp", "extraRowValue": "", "findBy": "cssSelector", "includeTags": false, "markEl": true, "multiple": false, "prefixText": "", "regex": "", "regexExp": [], "saveData": true, "selector": ".List\\(n\\):nth-child({{loopData.nDZcaN}}) > .Bgc\\(\\#fff\\)", "suffixText": "", "useTextContent": false, "variableName": "", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "omeitkf", "initialized": false, "label": "get-text", "position": {"x": 707.3696092994055, "y": 422.56847370888136}, "type": "BlockBasic"}, {"data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "id": "gwp0qrf", "initialized": false, "label": "trigger", "position": {"x": 39.23076923076928, "y": 350}, "type": "BlockBasic"}, {"data": {"active": true, "customUserAgent": false, "description": "", "disableBlock": false, "inGroup": false, "tabZoom": 1, "updatePrevTab": false, "url": "https://tw.stock.yahoo.com/future/options.html?opmr=optionfull&opcm=WTXO&opym=202507", "userAgent": "", "waitTabLoaded": true}, "id": "ys5s3ys", "initialized": false, "label": "new-tab", "position": {"x": 341.59540033773663, "y": 413.8400924192261}, "type": "BlockBasic"}, {"data": {"description": "", "disableBlock": false, "elementSelector": "", "fromNumber": 1, "loopData": "[]", "loopId": "nDZcaN", "loopThrough": "numbers", "maxLoop": 0, "referenceKey": "", "resumeLastWorkflow": false, "reverseLoop": false, "startIndex": 0, "toNumber": 100, "variableName": "", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "l8bldax", "initialized": false, "label": "loop-data", "position": {"x": 613.8461538461538, "y": 258.46153846153845}, "type": "BlockBasic"}, {"data": {"clearLoop": false, "disableBlock": false, "loopId": "nDZcaN"}, "id": "bpck030", "initialized": false, "label": "loop-breakpoint", "position": {"x": 859.2307692307692, "y": 579.2307692307692}, "type": "BlockLoopBreakpoint"}, {"data": {"addBOMHeader": true, "csvDelimiter": ",", "dataToExport": "data-columns", "description": "", "disableBlock": false, "name": "my test", "onConflict": "uniquify", "refKey": "", "type": "json", "variableName": ""}, "id": "anawwgx", "initialized": false, "label": "export-data", "position": {"x": 1307.6923076923076, "y": 299.2307692307692}, "type": "BlockBasic"}, {"data": {"activeTab": true, "allWindows": false, "closeType": "tab", "description": "", "disableBlock": false, "url": ""}, "id": "t7k8nvi", "initialized": false, "label": "close-tab", "position": {"x": 1080.384615384615, "y": 469.23076923076917}, "type": "BlockBasic"}], "position": [-696, -69], "viewport": {"x": -696, "y": -69, "zoom": 1.3}, "zoom": 1.3}, "settings": {"blockDelay": 0, "debugMode": false, "defaultColumnName": "column", "execContext": "popup", "executedBlockOnWeb": false, "inputAutocomplete": true, "insertDefaultColumn": false, "notification": true, "onError": "stop-workflow", "publicId": "", "restartTimes": 3, "reuseLastState": false, "saveLog": true, "tabLoadTimeout": 30000}, "globalData": "{\n\t\"key\": \"value\"\n}", "description": ""}, "A5l7jFdVLP1IeyGRCgRdy": {"extVersion": "1.29.10", "name": "pasteCVS", "icon": "riGlobalLine", "table": [], "version": "1.29.10", "drawflow": {"edges": [{"data": {}, "id": "vueflow__edge-nFm7iGcjJy4NI_9ryYLKinFm7iGcjJy4NI_9ryYLKi-output-1-3gg5hgp3gg5hgp-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "nFm7iGcjJy4NI_9ryYLKi", "sourceHandle": "nFm7iGcjJy4NI_9ryYLKi-output-1", "sourceX": 315.9999511330193, "sourceY": 544.4999497928944, "target": "3gg5hgp", "targetHandle": "3gg5hgp-input-1", "targetX": 352.1538272374676, "targetY": 416.0000117439348, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-bv120z0bv120z0-output-1-efq8ddlefq8ddl-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "bv120z0", "sourceHandle": "bv120z0-output-1", "sourceX": 842.4823616660176, "sourceY": 700.4778294037582, "target": "efq8ddl", "targetHandle": "efq8ddl-input-1", "targetX": 773.4131667404512, "targetY": 563.2179640952227, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-3gg5hgp3gg5hgp-output-1-bv120z0bv120z0-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "3gg5hgp", "sourceHandle": "3gg5hgp-output-1", "sourceX": 592.153735335825, "sourceY": 416.0000117439348, "target": "bv120z0", "targetHandle": "bv120z0-input-1", "targetX": 602.4823916166198, "targetY": 700.4778294037582, "type": "custom", "updatable": true}, {"data": {}, "id": "vueflow__edge-efq8ddlefq8ddl-output-1-pzulpqlpzulpql-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "efq8ddl", "sourceHandle": "efq8ddl-output-1", "sourceX": 1013.413136789849, "sourceY": 563.2179640952227, "target": "pzulpql", "targetHandle": "pzulpql-input-1", "targetX": 849.9202204493618, "targetY": 397.6057745026256, "type": "custom", "updatable": true}, {"class": "connected-edges", "data": {}, "id": "vueflow__edge-pzulpqlpzulpql-output-1-ioxsjowioxsjow-input-1", "label": "", "markerEnd": "arrowclosed", "selectable": true, "source": "pzulpql", "sourceHandle": "pzulpql-output-1", "sourceX": 1089.920128547719, "sourceY": 397.6057745026256, "target": "ioxsjow", "targetHandle": "ioxsjow-input-1", "targetX": 1131.4876374699193, "targetY": 399.72013580119113, "type": "custom", "updatable": true}], "nodes": [{"data": {"activeInInput": false, "contextMenuName": "", "contextTypes": [], "date": "", "days": [], "delay": 5, "description": "", "disableBlock": false, "interval": 60, "isUrlRegex": false, "observeElement": {"baseElOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}, "baseSelector": "", "matchPattern": "", "selector": "", "targetOptions": {"attributeFilter": [], "attributes": false, "characterData": false, "childList": true, "subtree": false}}, "parameters": [], "preferParamsInTab": false, "shortcut": "", "time": "00:00", "type": "manual", "url": ""}, "id": "nFm7iGcjJy4NI_9ryYLKi", "initialized": false, "label": "trigger", "position": {"x": 100, "y": 508.5}, "type": "BlockBasic"}, {"data": {"activeTab": true, "createIfNoMatch": false, "description": "", "disableBlock": false, "findTabBy": "match-patterns", "matchPattern": "*://docs.google.com/*", "tabIndex": 0, "tabTitle": "", "url": ""}, "id": "3gg5hgp", "initialized": false, "label": "switch-tab", "position": {"x": 376.15384615384613, "y": 380}, "type": "BlockBasic"}, {"data": {"action": "press-key", "description": "", "disableBlock": false, "keys": "Enter", "keysToPress": "", "pressTime": "100", "selector": "input#t-name-box"}, "id": "efq8ddl", "initialized": false, "label": "press-key", "position": {"x": 797.4131856568297, "y": 527.2179523512879}, "type": "BlockBasic"}, {"data": {"assignVariable": false, "clearValue": true, "dataColumn": "", "delay": 0, "description": "", "disableBlock": false, "events": [], "findBy": "cssSelector", "getValue": false, "markEl": false, "multiple": false, "optionPosition": "1", "saveData": false, "selectOptionBy": "value", "selected": true, "selector": "input#t-name-box", "type": "text-field", "value": "XslT_Q!A306", "variableName": "", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "bv120z0", "initialized": false, "label": "forms", "position": {"x": 626.4824105329983, "y": 664.4778176598234}, "type": "BlockBasic"}, {"data": {"description": "", "disableBlock": false, "eventName": "keydown", "eventParams": {"altKey": false, "bubbles": true, "cancelable": true, "code": "KeyV", "ctrlKey": true, "key": "V", "keyCode": 86, "metaKey": false, "repeat": false, "shiftKey": false}, "eventType": "keyboard-event", "findBy": "cssSelector", "markEl": false, "multiple": false, "selector": "html", "waitForSelector": false, "waitSelectorTimeout": 5000}, "id": "ioxsjow", "initialized": false, "label": "trigger-event", "position": {"x": 1155.4875944352573, "y": 363.7201550327765}, "type": "BlockBasic"}, {"data": {"disableBlock": false, "time": "1000"}, "id": "pzulpql", "initialized": false, "label": "delay", "position": {"x": 873.9202393657403, "y": 339.01203246011283}, "type": "BlockDelay"}], "position": [-491, -95], "viewport": {"x": -491, "y": -95, "zoom": 0.9852157682317587}, "zoom": 0.9852157682317587}, "settings": {"blockDelay": 0, "debugMode": false, "defaultColumnName": "column", "execContext": "popup", "executedBlockOnWeb": false, "inputAutocomplete": true, "insertDefaultColumn": false, "notification": true, "onError": "stop-workflow", "publicId": "", "restartTimes": 3, "reuseLastState": false, "saveLog": true}, "globalData": "{\n\t\"key\": \"value\"\n}", "description": ""}}}